<template>
  <div class="excalidraw-wrapper">
    <!-- 仅在客户端渲染时显示容器 -->
    <div v-if="isClient" ref="container" :style="{ height: '600px', border: '1px solid #eee' }">
    </div>
    <div v-else>加载中...</div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, onUnmounted, nextTick } from 'vue';
import { Excalidraw, type ExcalidrawProps } from '@excalidraw/excalidraw';
import React from 'react';
import ReactDOM from 'react-dom/client';
import '@excalidraw/excalidraw/index.css'; // 必须引入样式

// 标记是否为客户端环境（解决 SSR 问题）
const isClient = ref(false);
// 容器 DOM 引用
const container = ref(null);
// 存储 React 根实例，用于卸载时清理
let reactRoot = null;

// 确保在客户端环境下执行（关键！）
onMounted(() => {
  // 验证是否为浏览器环境
  if (typeof window !== 'undefined') {
    isClient.value = true;
    // 等待 Vue 容器 DOM 完全渲染
    nextTick(() => {
      if (container.value) {
        // 初始化 React 根实例并渲染 Excalidraw
        reactRoot = ReactDOM.createRoot(container.value);
        console.log(reactRoot);
        // 定义顶部右侧UI（用React.createElement替代JSX）
        const renderTopRightUI: ExcalidrawProps['renderTopRightUI'] = () => {
          // 按钮点击事件
          const handleClick = () => {
            window.alert("This is dummy top right UI");
          };
          // 用React.createElement创建按钮（避免JSX语法）
          return React.createElement(
            'button',
            {
              style: {
                background: "#70b1ec",
                border: "none",
                color: "#fff",
                width: "max-content",
                fontWeight: "bold",
                padding: "8px 12px",
                cursor: "pointer",
              },
              onClick: handleClick,
            },
            'Click me' // 按钮文本
          );
        };

        // 初始化Excalidraw配置
        const excalidrawConfig: ExcalidrawProps = {
          initialData: {
            elements: [
              {
                type: "rectangle",
                version: 141,
                versionNonce: 361174001,
                isDeleted: false,
                id: "oDVXy8D6rom3H1-LLH2-f",
                fillStyle: "hachure",
                strokeWidth: 1,
                strokeStyle: "solid",
                roughness: 1,
                opacity: 100,
                x: 100.50390625,
                y: 93.67578125,
                backgroundColor: "transparent",
              },
            ],
            appState: { zenModeEnabled: true, viewBackgroundColor: "#a5d8ff" },
            scrollToContent: true,

          },
          renderTopRightUI, // 传入自定义UI 按钮
          langCode: "zh-CN", //中文
          onChange: onChange, //自定义监听事件
          onLibraryChange: onLibraryChange,
          excalidrawAPI: excalidrawAPI,
        };
        //绑定自定义属性等内容
        reactRoot.render(React.createElement(Excalidraw, excalidrawConfig));
      }
    });
  }
});

// 组件卸载时清理 React 实例（防止内存泄漏）
onUnmounted(() => {
  if (reactRoot) {
    reactRoot.unmount();
  }
});

const onChange = (e) => {
  console.log(e);
  // localStorage.setItem("excalidraw-elements", JSON.stringify(e));
}
const onLibraryChange = (e) => {
  console.log(e);
  // localStorage.setItem("excalidraw-libs", JSON.stringify(e));
}
const excalidrawAPI = (e) => {
  console.log(e);
}
</script>