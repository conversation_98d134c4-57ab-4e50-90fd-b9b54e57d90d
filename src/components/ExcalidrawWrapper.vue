<template>
  <div class="excalidraw-wrapper">
    <!-- 仅在客户端渲染时显示容器 -->
    <div v-if="isClient" ref="container" 
    :style="{ height: '600px', border: '1px solid #eee' }">
  </div>
    <div v-else>加载中...</div>
  </div>
</template>

<script setup>
import { onMounted, ref, onUnmounted ,nextTick } from 'vue';
import { Excalidraw } from '@excalidraw/excalidraw';
import React from 'react';
import ReactDOM from 'react-dom/client';
import '@excalidraw/excalidraw/index.css'; // 必须引入样式

// 标记是否为客户端环境（解决 SSR 问题）
const isClient = ref(false);
// 容器 DOM 引用
const container = ref(null);
// 存储 React 根实例，用于卸载时清理
let reactRoot = null;

// 确保在客户端环境下执行（关键！）
onMounted(() => {
  // 验证是否为浏览器环境
  if (typeof window !== 'undefined') {
    isClient.value = true;
    // 等待 Vue 容器 DOM 完全渲染
    nextTick(() => {
      if (container.value) {
        // 初始化 React 根实例并渲染 Excalidraw
        reactRoot = ReactDOM.createRoot(container.value);
        reactRoot.render(React.createElement(Excalidraw, {
          // 可添加 Excalidraw 配置项，如默认主题
          theme: 'light',
        }));
      }
    });
  }
});

// 组件卸载时清理 React 实例（防止内存泄漏）
onUnmounted(() => {
  if (reactRoot) {
    reactRoot.unmount();
  }
});
</script>